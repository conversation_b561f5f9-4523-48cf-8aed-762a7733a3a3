/* Import Google font - Audiowide */
@import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Audiowide", sans-serif;
}

body {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 15px;
  align-items: center;
  justify-content: center;
  background: #FFD1DC; /* Match birthday website background */
}

.game-title {
  font-size: 1.8rem;
  color: #734f96;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: bold;
}

.try-again-btn {
  margin-top: 2rem;
  padding: 0.8rem 2.5rem;
  font-size: 1.2rem;
  border: none;
  border-radius: 2rem;
  background: #FFE4EC;
  color: #734f96;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  transition: background 0.2s;
  font-weight: bold;
}

.try-again-btn:hover {
  background: #F8C3E6;
}

.container {
  display: flex;
  flex-direction: column;
  max-width: 535px;
  width: 100%;
  padding: 2rem 5rem;
  border-radius: 14px;
  background: #FFE4EC; /* Match birthday website button color */
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.result_images {
  display: flex;
  gap: 7rem;
  justify-content: center;
}

.container.start .user_result {
  transform-origin: left;
  animation: userShake 0.7s ease infinite;
}

@keyframes userShake {
  50% {
    transform: rotate(10deg);
  }
}

.container.start .bot_result {
  transform-origin: right;
  animation: botShake 0.7s ease infinite;
}

@keyframes botShake {
  50% {
    transform: rotate(-10deg);
  }
}

.result_images img {
  width: 100px;
}

.user_result img {
  transform: rotate(90deg);
}

.bot_result img {
  transform: rotate(-90deg) rotateY(180deg);
}

.result {
  text-align: center;
  font-size: 2rem;
  color: #734f96; /* Match birthday website color scheme */
  margin: 1.5rem 0;
}

.option_image img {
  width: 50px;
}

.option_images {
  display: flex;
  width: 100%;
  align-items: center;
  margin-top: 2.5rem;
  justify-content: space-evenly;
}

.container.start .option_images {
  pointer-events: none;
}

.option_image {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.option_image:hover {
  opacity: 1;
}

.option_image.active {
  opacity: 1;
}

.option_image img {
  pointer-events: none;
}

.option_image p {
  color: #734f96; /* Match birthday website color scheme */
  font-size: 1.235rem;
  margin-top: 1rem;
  pointer-events: none;
}

/* Responsive media query code for small devices */
@media (max-width: 768px) {
  .container {
    padding: 2rem;
  }

  .result_images img {
    width: 80px;
  }
}

/* Responsive media query code for small devices */
@media (max-width: 500px) {
  .option_images {
    justify-content: space-between;
  }

  .option_image img {
    width: 40px;
  }
}