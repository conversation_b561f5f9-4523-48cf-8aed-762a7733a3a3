
/* Responsive Rock Paper Scissors game styles */
#game-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100vw;
    box-sizing: border-box;
    padding: 0;
}

#rps-container {
    background: #fff6fa;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    padding: 32px 18px;
    max-width: 400px;
    width: 90vw;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.result_field {
    width: 100%;
    margin-bottom: 2em;
    text-align: center;
}

.result_images {
    display: flex;
    justify-content: center;
    gap: 2em;
    margin-bottom: 1em;
}

.result_images img {
    width: 64px;
    height: 64px;
    object-fit: contain;
}

.option_images {
    display: flex;
    justify-content: center;
    gap: 2em;
    margin-top: 1em;
}

.option_image {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: transform 0.2s;
}

.option_image img {
    width: 56px;
    height: 56px;
    object-fit: contain;
}

.option_image.active {
    transform: scale(1.15);
    box-shadow: 0 2px 12px #e3b6a9;
}

#try-again-btn {
    margin-top: 2em;
    width: 100%;
    max-width: 300px;
    font-size: 1.2em;
}

@media (max-width: 500px) {
    #rps-container {
        padding: 18px 4px;
        max-width: 98vw;
    }
    .result_images img, .option_image img {
        width: 44px;
        height: 44px;
    }
}
/* Letter page styles */
.letter-container {
    max-height: 70vh;
    overflow-y: auto;
    background: #FFD1DC;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    width: 80vw;
    max-width: 600px;
    margin: 0 auto;
    padding: 40px 32px;
    position: relative;
}

.letter-content {
    background: repeating-linear-gradient(
        to bottom,
        transparent 0px,
        transparent 22px,
        #e3b6a9 23px,
        transparent 24px
    );
    font-family: 'Rush Flow', cursive, sans-serif;
    font-size: 1.5rem;
    color: #734f96;
    min-height: 400px;
    padding: 24px 18px;
    border-radius: 12px;
    box-sizing: border-box;
}
.polaroid-frame {
    position: absolute;
    background: #fff;
    border: 6px solid #eee;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    pointer-events: auto;
    z-index: 1;
}
/* Polaroid frames for birthday page */
/* Polaroid gallery layout */
.polaroid.landscape {
    width: 270px;
    height: 170px;
    padding: 10px;
    border-radius: 0;
}
.polaroid.landscape img.landscape-img {
    width: 250px;
    height: 150px;
    object-fit: cover;
    border-radius: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
#polaroid-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}
.wrapper {
    position: relative;
    width: 100vw;
    height: 100vh;
    max-width: 100vw;
    margin: 0;
}
.item {
    position: absolute;
    /* Remove flexbox so left/top works everywhere */
}
.polaroid {
    background: #fff;
    border: 2px solid #eee;
    box-shadow: 0 4px 12px rgba(0,0,0,0.18);
        border-radius: 0;
    padding: 12px 12px 36px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: box-shadow 0.3s, transform 0.3s;
    position: relative;
}
.polaroid img {
    width: 180px;
    height: 200px;
    object-fit: cover;
        border-radius: 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
.polaroid .caption {
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    text-align: center;
    font-family: 'Rush Flow', cursive;
    font-size: 1.15em;
    color: #734f96;
    letter-spacing: 1px;
    background: rgba(255,255,255,0.85);
    border-radius: 0 0 8px 8px;
    padding: 4px 0;
}
.polaroid:hover {
    box-shadow: 0 12px 24px rgba(0,0,0,0.28);
    transform: scale(1.05) rotate(-2deg);
    z-index: 3;
}
/* Audio button group for card controls */
.card-audio-btn-group {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 64px;
    width: 100%;
}

.card-audio-btn, .card-play-btn {
    background: none;
    border: none;
    box-shadow: none;
    width: auto;
    height: auto;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
}

.card-audio-btn .fa,
.card-play-btn .fa {
    color: #734f96 !important;
    font-size: 2rem;
}
.portrait-card {
    width: 340px;
    height: 520px;
    background: #fff;
        border-radius: 0;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 0;
    position: relative;
    animation: reverse-dissolve 1s forwards;
}
.card-image-container {
    width: 100%;
    height: 75%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top-left-radius: 32px;
    border-top-right-radius: 32px;
    overflow: hidden;
}
.card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-top-left-radius: 32px;
    border-top-right-radius: 32px;
}
.card-song-name {
    width: 100%;
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #111;
    margin: 16px 0 8px 0;
}
.card-audio-controls {
    width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}
.card-play-btn {
    background: none;
    border: none;
    box-shadow: none;
    width: auto;
    height: auto;
    font-size: 2rem;
    margin-bottom: 8px;
    cursor: pointer;
    position: relative;
    top: 5px;
}

.card-play-btn .fa-play {
    color: lavender;
}
.card-play-btn:hover {
    background: none;
}
.card-play-btn .fa-play,
.card-play-btn:hover .fa-play {
    color: #734f96 !important;
}
.card-audio-bar {
    width: 100%;
    accent-color: #734f96;
    margin-top: 4px;
    height: 4px;
    border: none;
    background: #e0e0e0;
    box-shadow: none;
}
.reverse-dissolve {
    animation: reverse-dissolve 1s forwards;
}

@keyframes reverse-dissolve {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
/* Page visibility and centering logic */
.page {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2;
}
.page.active {
    display: flex;
}
/* Responsive font size for greeting text */
@media (max-width: 600px) {
    .center-message {
        font-size: 3rem;
    }
}
#greeting-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2;
}

#greeting-text, .center-message {
    position: relative;
    left: auto;
    top: auto;
    transform: rotate(-7deg);
    text-align: center;
    /* other styles... */
}
.dissolve {
    animation: dissolve 1s forwards;
}

@keyframes dissolve {
    0% { opacity: 1; }
    100% { opacity: 0; }
}
.oval-btn {
    margin-top: 2rem;
    padding: 0.8rem 2.5rem;
    font-size: 2rem;
    border: none;
    border-radius: 2rem;
    background: #FFE4EC;
    color: #B57EDC;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: background 0.2s;
}
.oval-btn:hover {
    background: #F8C3E6;
}
/* .page class removed from greeting-page to avoid conflicting centering styles */

.play-btn {
    padding: 1rem 2.5rem;
    font-size: 2rem;
    border: none;
    border-radius: 2rem;
    background: #B57EDC;
    color: #FFE4EC;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: background 0.2s;
    margin-top: 6rem;
}
.play-btn:hover {
    background: #A05FCB;
}
.circle-arrow {
        position: absolute;
        top: calc(50% + 100px);
        left: 50%;
        transform: translate(-50%, 0);
    width: 70px;
    height: 70px;
    background: #B57EDC; /* Lavender */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3;
}

.circle-arrow .arrow {
        width: 32px;
        height: 32px;
        display: block;
        fill: #FFE4EC; /* Background color for arrow */
        transform: rotate(-90deg);
}
.gift {
    position: absolute;
    font-size: 1.5rem;
    color: #7E57C2; /* medium lavender for gift outline */
    opacity: 0;
    transition: opacity 2s;
    transform: rotate(var(--rotate, 0deg));
}
@font-face {
    font-family: 'Rush Flow';
    src:  url('fonts/RushFlow.otf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.center-message {
    font-family: 'Rush Flow', cursive, sans-serif;
    font-size: 6.5rem;
    color: #B57EDC; /* Lavender */
    text-align: center;
    letter-spacing: 2px;
    z-index: 2;
}
body {
    background: #FFE4EC;
    margin: 0;
    padding: 0;
    overflow: hidden;
}
    #hearts-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        z-index: 1;
    }

 .heart {
    position: absolute;
    font-size: 1.5rem;
    color: #FF69B4; /* lighter pink, stands out from background */
    opacity: 0;
    transition: opacity 2s;
    transform: rotate(var(--rotate, 0deg));
}

body {
    background: #FFD1DC;
}
/* Baby pink background for the page */
.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    background: #FFD1DC;
    overflow: hidden;
}
.player-stack {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  position: relative;
  width: 450px;
  height: 340px;
}
.player-card {
        width: 184px;
        height: 104px;
        object-fit: cover;
        border-radius: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 50%;
  top: 0;
  transition: box-shadow 0.2s;
}
.card-left {
  transform: translate(-140px, 25px) rotate(-18deg);
  opacity: 0.75;
  z-index: 1;
}
.card-center {
  transform: translate(-50%, 0px) scale(1.1);
  z-index: 2;
  box-shadow: 0 4px 28px #111;
  background: #272525;
}
.card-right {
  transform: translate(40px, 25px) rotate(18deg);
  opacity: 0.75;
  z-index: 1;
}
.album-art {
  width: 90%;
  height: 180px;
  border-radius: 16px;
  margin-top: 24px;
  object-fit: cover;
  box-shadow: 0 2px 18px #b94042cc;
}
.track-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 18px 0 4px;
  text-align: center;
}
.artist {
  font-size: 0.98rem;
  color: #e3b6a9;
  margin-bottom: 12px;
  text-align: center;
}
.player-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 18px;
  gap: 28px;
}
.player-controls button {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.8rem;
  cursor: pointer;
  filter: drop-shadow(0 0 3px #a94243);
  outline: none;
}
