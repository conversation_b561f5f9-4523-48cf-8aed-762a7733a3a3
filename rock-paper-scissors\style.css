/* Import Google font - Audiowide */
@import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Audiowide", sans-serif;
}

body {
  height: 100vh;
  display: flex;
  padding: 15px;
  align-items: center;
  justify-content: center;
  background: #f6f7fb;
}

.container {
  display: flex;
  flex-direction: column;
  max-width: 535px;
  width: 100%;
  padding: 2rem 5rem;
  border-radius: 14px;
  background: #fff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.result_images {
  display: flex;
  gap: 7rem;
  justify-content: center;
}

.container.start .user_result {
  transform-origin: left;
  animation: userShake 0.7s ease infinite;
}

@keyframes userShake {
  50% {
    transform: rotate(10deg);
  }
}

.container.start .bot_result {
  transform-origin: right;
  animation: botShake 0.7s ease infinite;
}

@keyframes botShake {
  50% {
    transform: rotate(-10deg);
  }
}

.result_images img {
  width: 100px;
}

.user_result img {
  transform: rotate(90deg);
}

.bot_result img {
  transform: rotate(-90deg) rotateY(180deg);
}

.result {
  text-align: center;
  font-size: 2rem;
  color: #7d2ae8;
  margin: 1.5rem 0;
}

.option_image img {
  width: 50px;
}

.option_images {
  display: flex;
  width: 100%;
  align-items: center;
  margin-top: 2.5rem;
  justify-content: space-evenly;
}

.container.start .option_images {
  pointer-events: none;
}

.option_image {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.option_image:hover {
  opacity: 1;
}

.option_image.active {
  opacity: 1;
}

.option_image img {
  pointer-events: none;
}

.option_image p {
  color: #7d2ae8;
  font-size: 1.235rem;
  margin-top: 1rem;
  pointer-events: none;
}

/* Responsive media query code for small devices */
@media (max-width: 768px) {
  .container {
    padding: 2rem;
  }

  .result_images img {
    width: 80px;
  }
}

/* Responsive media query code for small devices */
@media (max-width: 500px) {
  .option_images {
    justify-content: space-between;
  }

  .option_image img {
    width: 40px;
  }
}