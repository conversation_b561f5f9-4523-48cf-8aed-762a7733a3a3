<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Birthday</title>
    <link rel="stylesheet" href="css.css">
    <!-- Font Awesome CDN for heart outline -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>

            <div id="hearts-container"></div>
            <div id="greeting-page" class="page active">
                <div id="greeting-text" class="center-message">Hey!</div>
                <button id="hey-btn" class="oval-btn">Hey?</button>
            </div>
            <div id="player-page" class="page">
                <div id="player-text" class="center-message" style="font-size:6.5rem;">Ready for a surprise?</div>
                <button id="play-btn" class="play-btn">Yes?</button>
                <audio id="birthday-audio" src="audio/birthday.mp3" preload="auto"></audio>
            </div>
            <div id="birthday-page" class="page">
                <div id="music-message" class="center-message" style="display:none;">Some Music would be Good</div>
                <audio id="husn-audio" src="songs/Anuv Jain - Husn.mp3"></audio>
                <div id="happy-birthday-text" style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:7rem;font-family:'Rush Flow',cursive,sans-serif;color:#734f96;text-align:center;display:none;z-index:10;">Happy Birthday</div>
                <button id="show-letter-btn" class="oval-btn" style="position:absolute;bottom:48px;left:50%;transform:translateX(-50%);z-index:20;display:none;">Open</button>
                                <div id="polaroid-container" style="display:none;z-index:1;">
                                                        <div class="wrapper">
                                                                                                                        <div class="item" style="position:absolute; left:153px; top:121px; transform:rotate(-8deg);">
                                                                                                                            <div class="polaroid landscape">
                                                                                                                                <img src="photos/1.jpg" alt="Photo 1" class="landscape-img">
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                <div class="item" style="position:absolute; left:1074px; top:39px;">
                                                                                    <div class="polaroid">
                                                                                        <img src="photos/2.jpg" alt="Photo 2">
                                                                                    </div>
                                                                                </div>
                                                                                <div class="item" style="position:absolute; left:710px; top:621px;">
                                                                                    <div class="polaroid">
                                                                                        <img src="photos/3.jpg" alt="Photo 3">
                                                                                    </div>
                                                                                </div>
                                                                                                                        <div class="item" style="position:absolute; left:1363px; top:280px; transform:rotate(10deg);">
                                                                                                                            <div class="polaroid landscape">
                                                                                                                                <img src="photos/4.jpg" alt="Photo 4" class="landscape-img">
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        <div class="item" style="position:absolute; left:1220px; top:640px; transform:rotate(-12deg);">
                                                                                                                            <div class="polaroid landscape">
                                                                                                                                <img src="photos/5.jpg" alt="Photo 5" class="landscape-img">
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                <div class="item" style="position:absolute; left:670px; top:64px;">
                                                                                    <div class="polaroid">
                                                                                        <img src="photos/6.jpg" alt="Photo 6">
                                                                                    </div>
                                                                                </div>
                                                                                <div class="item" style="position:absolute; left:130px; top:491px;">
                                                                                    <div class="polaroid">
                                                                                        <img src="photos/7.jpg" alt="Photo 7">
                                                                                    </div>
                                                                                </div>
                                                        </div>
                                </div>
                </div>
            </div>
            <div id="letter-page" class="page">
            <div class="letter-container reverse-dissolve" id="letter-flip">
                <div class="letter-content">
<p>Hey so Happy Birthday Ig?</p>
<p>I'm pretty sure this will be late or after your birthday—if it is, then Sorry!</p>
<p>Sooooooo how was your day? I don't know where to start. I can see you've changed a lot—it's almost like you're a different person. I guess I changed too? idk ni sollu.</p>
<p>And also yeah, I AM grateful that you are/were a part of my life etc etc. Idk I just randomly think about it then go into a spiral of thoughts.</p>
<p>I don't know what I'm doing with my life tbh like BRUH onnume solradhuku illa.</p>
<p>So about the moving on part, idk if it was easy or hard for you but as I said above, whenever I even think about it I just... can't—neither forget nor accept it ¯\_(ツ)_/¯</p>
<p>I don't really have anyone (for emotional support) so I mostly avoid it just so I don't want to have a breakdown (been a long time since I had one).</p>
<p>Though I don't think I'm fit/good to be in a relationship, I just can't move on? (or I have convinced myself I don't want to Ig idk?)</p>
<p>So yeah, that's that. I really wanted to see/talk to you but all I could remember was you saying "we can't meet". The last time I saw you before the wedding was when you were on a video call with Mahil ig? I was in his home. IRL the car ride ig? we 4 went.</p>
<p>Was really Happy to see you that Day but I wasn't sure if I should approach you or not? Varadha venama ndra alavuku nenachutu irunthen since you said that. So yeah that was good?</p>
<p>Vera enna sollalam 🤔 neeye kelu etha kelvi, idk what else to say.</p>
<p>Really wanted to be the first person to wish you but nadakkavillai ik not your fault, atha nenachutu than poi paduthuten hence padikavum illa ithayum mudiakla 🗿</p>
<p>Hope you had a great Birthday.</p>
<p>PS: Can't beleive you're 19 already next year aunty dhan.</p>
<p style="margin-top:2em;">With Lots of Love,<br>Karunesh</p>
                    </div>
                </div>
            </div>
            <script src="script.js"></script>
    <script src="script.js"></script>
</body>
</html>
