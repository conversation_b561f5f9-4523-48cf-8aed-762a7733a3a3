function createGift() {
    const gift = document.createElement('i');
    gift.className = 'fa-solid fa-gift gift';
    const { x, y } = randomPosition();
    gift.style.left = `${x}px`;
    gift.style.top = `${y}px`;
    // Random rotation between -45deg and 45deg
    gift.style.setProperty('--rotate', `${Math.random() * 90 - 45}deg`);
    document.getElementById('hearts-container').appendChild(gift);
    // Fade in
    setTimeout(() => {
        gift.style.opacity = 1;
    }, 100);
    // Fade out and remove
    setTimeout(() => {
        gift.style.opacity = 0;
        setTimeout(() => gift.remove(), 2000);
    }, 2000);
}
function randomPosition() {
    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;
    return { x, y };
}


function createHeart() {
    const heart = document.createElement('i');
    heart.className = 'fa-regular fa-heart heart';
    const { x, y } = randomPosition();
    heart.style.left = `${x}px`;
    heart.style.top = `${y}px`;
    // Random rotation between -45deg and 45deg
    heart.style.setProperty('--rotate', `${Math.random() * 90 - 45}deg`);
    document.getElementById('hearts-container').appendChild(heart);
    // Fade in
    setTimeout(() => {
        heart.style.opacity = 1;
    }, 100);
    // Fade out and remove
    setTimeout(() => {
        heart.style.opacity = 0;
        setTimeout(() => heart.remove(), 2000);
    }, 2000);
}


function spawnHearts() {
    setInterval(() => {
        // Spawn 5-10 hearts and gifts at a time
        const count = Math.floor(Math.random() * 6) + 5;
        for (let i = 0; i < count; i++) {
            if (i % 2 === 0) {
                createHeart();
            } else {
                createGift();
            }
        }
    }, Math.random() * 2000 + 2000); // random interval between 2s and 4s
}


function showPage(id) {
    document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
    document.getElementById(id).classList.add('active');
}

function preloadAudio() {
    const audio = document.getElementById('birthday-audio');
    audio.load();
}

window.onload = function() {
    // Helper: Navigation between pages
    function showPage(id) {
        document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
        document.getElementById(id).classList.add('active');
    }

    // Helper: Add & remove animation classes for letter
    function animateAndShowLetter() {
        const letterPage = document.getElementById('letter-page');
        const letterContainer = document.getElementById('letter-flip');
        showPage('letter-page');
        letterContainer.classList.add('reverse-dissolve');
        setTimeout(() => {
            letterContainer.classList.remove('reverse-dissolve');
        }, 1000);
    }

    // Show RPS game after "Open" is pressed
    const showLetterBtn = document.getElementById('show-letter-btn');
    if (showLetterBtn) {
        showLetterBtn.onclick = function() {
            showPage('game-page');
            document.getElementById('try-again-btn').style.display = 'none';
            // Reset game message/result
            const result = document.querySelector('#rps-container .result');
            if(result) result.textContent = "Let's Play!";
        };
    }

    // RPS game logic
    const rpsContainer = document.getElementById('rps-container');
    const userResult = rpsContainer.querySelector('.user_result img');
    const botResult = rpsContainer.querySelector('.bot_result img');
    const result = rpsContainer.querySelector('.result');
    const optionImages = rpsContainer.querySelectorAll('.option_image');
    const tryAgainBtn = document.getElementById('try-again-btn');
    const botImages = ["images/rock.png", "images/paper.png", "images/scissors.png"];
    const outcomes = {
        RR: "Draw", RP: "BOT", RS: "YOU",
        PP: "Draw", PR: "YOU", PS: "BOT",
        SS: "Draw", SR: "BOT", SP: "YOU"
    };

    optionImages.forEach((image, clickedIndex) => {
        image.onclick = function() {
            userResult.src = botResult.src = "images/rock.png";
            result.textContent = "Wait...";
            optionImages.forEach((img, idx) => {
                img.classList.toggle("active", idx === clickedIndex);
            });
            rpsContainer.classList.add("start");
            setTimeout(() => {
                rpsContainer.classList.remove("start");
                const userImageSrc = image.querySelector("img").src;
                userResult.src = userImageSrc;
                const randomNumber = Math.floor(Math.random() * botImages.length);
                botResult.src = botImages[randomNumber];
                const userValue = ["R","P","S"][clickedIndex];
                const botValue = ["R","P","S"][randomNumber];
                const outcome = outcomes[userValue + botValue] || "Unknown";

                if (userValue === botValue) {
                    result.textContent = "Match Draw";
                    tryAgainBtn.style.display = '';
                } else if (outcome === "YOU") {
                    result.textContent = "GAME WON";
                    tryAgainBtn.style.display = 'none';
                    setTimeout(() => {
                        // Fade out RPS/game page, reveal the letter
                        document.getElementById('game-page').classList.remove('active');
                        animateAndShowLetter();
                    }, 3000); // 3 seconds
                } else {
                    result.textContent = "You Lost!";
                    tryAgainBtn.style.display = '';
                }
            }, 1000);
        };
    });

    // Try again button: replay the RPS game (reload options and result)
    tryAgainBtn.onclick = function() {
        result.textContent = "Let's Play!";
        optionImages.forEach(img => img.classList.remove("active"));
        userResult.src = botResult.src = "images/rock.png";
        tryAgainBtn.style.display = 'none';
    };
    spawnHearts();
    preloadAudio();
    showPage('greeting-page');

    // Show RPS game after "Open" is pressed
    if (showLetterBtn) {
        showLetterBtn.onclick = function() {
            showPage('game-page');
            document.getElementById('try-again-btn').style.display = 'none';
            // Reset game message/result
            const result = document.querySelector('#rps-container .result');
            if(result) result.textContent = "Let's Play!";
        };
    }

    // Show Open button 5 seconds after polaroids appear
    const polaroids = document.getElementById('polaroid-container');
    if (polaroids) {
        const observer = new MutationObserver(() => {
            if (polaroids.style.display === 'block') {
                setTimeout(() => {
                    if (showLetterBtn) showLetterBtn.style.display = '';
                }, 5000);
                observer.disconnect();
            }
        });
        observer.observe(polaroids, { attributes: true, attributeFilter: ['style'] });
    }


    document.getElementById('hey-btn').onclick = function() {
        const greeting = document.getElementById('greeting-page');
        greeting.classList.add('dissolve');
        setTimeout(() => {
            showPage('player-page');
            // Add reverse dissolve to text and button
            const playerText = document.getElementById('player-text');
            const playBtn = document.getElementById('play-btn');
            playerText.classList.add('reverse-dissolve');
            playBtn.classList.add('reverse-dissolve');
            // Remove animation class after animation ends
            setTimeout(() => {
                playerText.classList.remove('reverse-dissolve');
                playBtn.classList.remove('reverse-dissolve');
            }, 1000);
        }, 1000); // after dissolve animation
    };

    document.getElementById('play-btn').onclick = function() {
        // Dissolve 'Ready for a surprise?'
        const playerText = document.getElementById('player-text');
        playerText.classList.add('dissolve');
        setTimeout(() => {
            showPage('birthday-page');
            // Show music message with reverse-dissolve
            const musicMsg = document.getElementById('music-message');
            if (musicMsg) {
                musicMsg.style.display = 'block';
                musicMsg.classList.add('reverse-dissolve');
                setTimeout(() => {
                    musicMsg.classList.remove('reverse-dissolve');
                    // Wait 2 seconds, then dissolve music message and show Happy Birthday
                    setTimeout(() => {
                        musicMsg.classList.add('dissolve');
                        setTimeout(() => {
                            musicMsg.style.display = 'none';
                            // Show Happy Birthday text
                            const hbText = document.getElementById('happy-birthday-text');
                            if (hbText) {
                                hbText.style.display = 'block';
                                hbText.classList.add('reverse-dissolve');
                                setTimeout(() => {
                                    hbText.classList.remove('reverse-dissolve');
                                }, 1000);
                            }
                        }, 1000);
                        // Show polaroids after Happy Birthday
                        setTimeout(() => {
                            const polaroids = document.getElementById('polaroid-container');
                            if (polaroids) {
                                polaroids.style.display = 'block';
                                polaroids.classList.add('reverse-dissolve');
                                setTimeout(() => {
                                    polaroids.classList.remove('reverse-dissolve');
                                }, 1000);
                            }
                            // Polaroid photo input logic
                            document.querySelectorAll('.polaroid-input').forEach(input => {
                                input.onchange = function(e) {
                                    const frame = input.parentElement;
                                    if (input.files && input.files[0]) {
                                        const reader = new FileReader();
                                        reader.onload = function(ev) {
                                            let img = frame.querySelector('img');
                                            if (!img) {
                                                img = document.createElement('img');
                                                frame.appendChild(img);
                                            }
                                            img.src = ev.target.result;
                                        };
                                        reader.readAsDataURL(input.files[0]);
                                    }
                                };
                            });
                        }, 1000);
                    }, 2000);
                }, 1000);
            }
            // Play Husn after message appears
            setTimeout(() => {
                const husnAudio = document.getElementById('husn-audio');
                if (husnAudio) husnAudio.play();
            }, 1200);
        }, 1000);
    };
};
