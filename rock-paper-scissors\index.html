<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Rock Paper Scissors</title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="game-title">Play Rock Paper Scissors to unlock your letter!</div>
  <section class="container">
    <!-- Result display section -->
    <div class="result_field">
        <!-- Container for user and BOT result images -->
        <div class="result_images">
            <span class="user_result">
                <img src="images/rock.png" alt="User's choice image" />
            </span>
            <span class="bot_result">
                <img src="images/rock.png" alt="BOT's choice image" />
            </span>
        </div>
        <!-- Display the game result message -->
        <div class="result">Let's Play!</div>
    </div>

    <!-- Options for user to choose from -->
    <div class="option_images">
        <span class="option_image">
            <img src="images/rock.png" alt="Rock image" />
            <p>Rock</p>
        </span>
        <span class="option_image">
            <img src="images/paper.png" alt="Paper image" />
            <p>Paper</p>
        </span>
        <span class="option_image">
            <img src="images/scissors.png" alt="Scissors image" />
            <p>Scissors</p>
        </span>
    </div>

    <button id="try-again-btn" class="try-again-btn" style="display: none;">Try Again?</button>
</section>

  <script src="script.js" defer></script>
</body>
</html>