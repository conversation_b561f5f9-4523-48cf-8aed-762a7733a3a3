# Copilot Instructions for AI Coding Agents

## Project Overview
This is a single-page web app designed for a birthday greeting experience. It uses vanilla HTML, CSS, and JavaScript, with no build system or framework. The app displays animated hearts and gifts, transitions between greeting, player, and birthday pages, and plays a birthday audio file.

## Architecture & Key Files
- `index.html`: Main entry point. Defines three page sections (`greeting-page`, `player-page`, `birthday-page`) and includes the hearts/gifts container and audio player.
- `css.css`: Handles all styling, including custom fonts, page transitions, and animated elements. Uses the `Rush Flow` font from `fonts/RushFlow.otf`.
- `script.js`: Controls page logic, heart/gift animations, audio playback, and page transitions. Uses Font Awesome icons via CDN.
- `fonts/`: Contains custom font used for headings and messages.
- `songs/`: Intended for birthday audio files (referenced in HTML as `audio/birthday.mp3`).

## Developer Workflow
- No build, test, or deployment scripts. All changes are made directly to source files.
- To preview, open `index.html` in a browser. No local server required.
- For debugging, use browser DevTools (console, inspector).
- To add or change the birthday song, update the `src` attribute of the `<audio>` tag in `index.html` and place the file in `songs/`.

## Project-Specific Patterns
- **Page transitions**: Controlled by toggling the `.active` class on `.page` containers. Only one page is visible at a time; JS adds/removes `.active` for transitions. Use the `.dissolve` animation class for fade effects if needed.
- **Animated elements**: Hearts and gifts are created dynamically in JS, positioned randomly, and faded in/out using CSS transitions and custom `--rotate` property.
- **Font Awesome**: Icons are loaded via CDN; ensure internet connectivity for proper rendering.
- **Custom font**: The `Rush Flow` font is loaded from the local `fonts/` directory via `@font-face` in CSS.
- **No external dependencies**: Other than Font Awesome CDN and local font/audio files.

## Conventions & Integration Points
- All interactive logic is in `script.js`. Use `.active` class toggling for page visibility instead of inline styles. Avoid splitting logic into multiple files unless refactoring for maintainability.
- Use semantic HTML and keep styles in `css.css`. All pages should use the `.page` class for flexbox centering and visibility control.
- Keep all assets (fonts, audio) in their respective folders for easy reference.
- No automated tests; manual browser testing is standard.

## Example Patterns
- To add a new animated icon, create a function in `script.js` similar to `createHeart()` or `createGift()` and update CSS for styling.
- To add a new page, duplicate a `.page` container in `index.html`, add your content, and use JS to toggle its `.active` class for transitions.
- For future scalability, always use `.page` and `.active` for show/hide logic. Add media queries in CSS for responsive font sizes and layout. Consider ARIA attributes and focus management for accessibility.

---
For questions or improvements, refer to `Readme.md` for a high-level summary. This file is the authoritative guide for AI agents working in this codebase.
